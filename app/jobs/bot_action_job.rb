class BotActionJob < ApplicationJob
  queue_as :default

  def perform(action, bot_id, user_id)
    @bot = Script.find_by(id: bot_id)
    @user = User.find_by(id: user_id)

    return unless @bot && @user

    # Ensure the bot belongs to the user
    return unless @bot.user_id == @user.id

    case action
    when 'start'
      start_bot
    when 'stop'
      stop_bot
    else
      Rails.logger.error("Unknown bot action: #{action}")
    end
  end

  private

  def start_bot
    response = ScriptManagerService.start_script(@bot.id)

    if response && response.success?
      # Broadcast success message
      broadcast_result('notice', "Bot #{@bot.type.downcase} was successfully started.")
    else
      # Broadcast error message
      broadcast_result('alert', "Failed to start bot #{@bot.type.downcase}.")
    end
  end

  def stop_bot
    response = ScriptManagerService.stop_script(@bot.id)

    if response && response.success?
      # Broadcast success message
      broadcast_result('notice', "Bot #{@bot.type.downcase} was successfully stopped.")
    else
      # Broadcast error message
      broadcast_result('alert', "Failed to stop bot #{@bot.type.downcase}.")
    end
  end
end
