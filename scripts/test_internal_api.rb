#!/usr/bin/env ruby
# This script tests the internal API endpoints for container communication

require 'httparty'
require 'json'

# Configuration
API_BASE_URL = ENV['API_BASE_URL'] || 'http://localhost:3000'
AUTH_KEY = ENV['INTERNAL_API_AUTH_KEY'] || 'test_auth_key'
MT5_SERVER_SEARCH_ENDPOINT = "#{API_BASE_URL}/api/internal/container/mt5_server_search"
SCRIPT_NOTIFICATION_ENDPOINT = "#{API_BASE_URL}/api/internal/container/script_notification"

# Test MT5 server search
def test_mt5_server_search(server_name, container_name = 'mt5')
  puts "Testing MT5 server search for server: #{server_name}, container: #{container_name}"

  response = HTTParty.post(
    MT5_SERVER_SEARCH_ENDPOINT,
    body: {
      server_name: server_name,
      container_name: container_name,
      auth_key: AUTH_KEY
    },
    headers: {
      'X-Internal-Auth-Key' => AUTH_KEY
    }
  )

  puts "Response status: #{response.code}"
  puts "Response body: #{JSON.pretty_generate(JSON.parse(response.body))}"
  puts "-" * 50
end

# Test script notification
def test_script_notification(script_id, status, message = nil)
  puts "Testing script notification for script_id: #{script_id}, status: #{status}"

  body = {
    script_id: script_id,
    status: status,
    auth_key: AUTH_KEY
  }
  body[:message] = message if message

  response = HTTParty.post(
    SCRIPT_NOTIFICATION_ENDPOINT,
    body: body,
    headers: {
      'X-Internal-Auth-Key' => AUTH_KEY
    }
  )

  puts "Response status: #{response.code}"
  puts "Response body: #{JSON.pretty_generate(JSON.parse(response.body))}"
  puts "-" * 50
end

# Test authentication failure
def test_auth_failure(endpoint)
  puts "Testing authentication failure for endpoint: #{endpoint}"

  response = HTTParty.post(
    endpoint,
    body: {
      server_name: 'test-server',
      script_id: 1,
      status: 'test'
    },
    headers: {
      'X-Internal-Auth-Key' => 'invalid_key'
    }
  )

  puts "Response status: #{response.code}"
  puts "Response body: #{JSON.pretty_generate(JSON.parse(response.body))}"
  puts "-" * 50
end

# Run tests
puts "=== Testing Internal API Endpoints ==="
puts

# Test MT5 server search with valid server
test_mt5_server_search('demo.exness.com')

# Test MT5 server search with invalid server
test_mt5_server_search('invalid-server.com')

# Test MT5 server search without server name
test_mt5_server_search('')

# Test script notification with valid script
test_script_notification(1, 'completed', 'Script completed successfully')

# Test script notification with invalid script
test_script_notification(999, 'failed', 'Script execution failed')

# Test script notification without required parameters
test_script_notification('', 'completed')
test_script_notification(1, '')

# Test authentication failures
puts "\n=== Testing Authentication Failures ==="
test_auth_failure(MT5_SERVER_SEARCH_ENDPOINT)
test_auth_failure(SCRIPT_NOTIFICATION_ENDPOINT)

puts "=== Tests Completed ==="
